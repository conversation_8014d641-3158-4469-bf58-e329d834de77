/* Professional Modal Styles */

.professional-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: #ffffff;
}

.professional-modal-dialog {
  margin: 1.75rem auto;
  max-width: none;
}

/* Responsive sizing with proper centering */
.professional-modal .modal-lg {
  max-width: 65vw;
  width: 65vw;
}

.professional-modal .modal-xl {
  max-width: 80vw;
  width: 80vw;
}

.professional-modal .modal-sm {
  max-width: 400px;
  width: 400px;
}

/* Ensure proper centering on all screen sizes */
@media (min-width: 576px) {
  .professional-modal .modal-dialog {
    margin: 1.75rem auto;
  }
}

@media (min-width: 992px) {
  .professional-modal .modal-lg {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Professional Header Styling */
.professional-modal-header {
  padding: 1rem 1.5rem;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  position: relative;
}

.professional-modal-header .modal-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

.professional-modal-header .modal-title-main {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0;
}

.professional-modal-header .modal-subtitle {
  font-size: 0.875rem;
  font-weight: 400;
  opacity: 0.9;
  margin-top: 0.25rem;
  line-height: 1.3;
}

.professional-modal-header .btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  opacity: 0.8;
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

.professional-modal-header .btn-close:hover {
  opacity: 1;
}

/* Header variants */
.professional-modal-header.bg-primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.professional-modal-header.bg-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.professional-modal-header.bg-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.professional-modal-header.bg-warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.professional-modal-header.bg-info {
  background: linear-gradient(135deg, #2196f3 0%, #0288d1 100%);
}

.professional-modal-header.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Professional Body Styling */
.professional-modal-body {
  padding: 1.5rem;
  background: #ffffff;
  min-height: 200px;
}

.professional-modal-body::-webkit-scrollbar {
  width: 6px;
}

.professional-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.professional-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.professional-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Professional Footer Styling */
.professional-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.professional-modal-footer .btn {
  min-width: 100px;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.5rem 1.25rem;
  transition: all 0.2s ease;
}

.professional-modal-footer .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.professional-modal-footer .btn:active {
  transform: translateY(0);
}

.professional-modal-footer .btn-primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: none;
}

.professional-modal-footer .btn-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  border: none;
}

.professional-modal-footer .btn-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  border: none;
}

.professional-modal-footer .btn-secondary {
  background: #6c757d;
  border: 1px solid #6c757d;
}

/* Loading states */
.professional-modal-footer .btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.professional-modal-footer .btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Animation for modal appearance */
.professional-modal .modal-dialog {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Backdrop styling */
.professional-modal .modal-backdrop {
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
  .professional-modal .modal-lg {
    max-width: 90vw;
    width: 90vw;
  }
  
  .professional-modal .modal-xl {
    max-width: 95vw;
    width: 95vw;
  }
  
  .professional-modal-header {
    padding: 0.875rem 1rem;
  }
  
  .professional-modal-body {
    padding: 1rem;
  }
  
  .professional-modal-footer {
    padding: 0.875rem 1rem;
  }
}

@media (max-width: 575.98px) {
  .professional-modal .modal-dialog {
    margin: 0.5rem;
  }
  
  .professional-modal .modal-lg,
  .professional-modal .modal-xl {
    max-width: calc(100vw - 1rem);
    width: calc(100vw - 1rem);
  }
  
  .professional-modal-header .modal-title-main {
    font-size: 1rem;
  }
  
  .professional-modal-footer {
    flex-direction: column;
  }
  
  .professional-modal-footer .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .professional-modal-footer .btn:last-child {
    margin-bottom: 0;
  }
}

/* Focus states for accessibility */
.professional-modal-header .btn-close:focus,
.professional-modal-footer .btn:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .professional-modal {
    display: none !important;
  }
}
